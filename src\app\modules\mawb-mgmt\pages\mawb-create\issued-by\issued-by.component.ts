import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';

@Component({
	selector: 'orll-issued-by',
	templateUrl: './issued-by.component.html',
	styleUrl: './issued-by.component.scss',
	imports: [TranslatePipe],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IssuedByComponent {
	@Input() issuedByInfo: OrgInfo | null = null;
	@Input() forHawb = false;

	getData(): (ShipmentParty & { airlineCode: string }) | null {
		if (!this.issuedByInfo) return null;

		const personInfo = this.issuedByInfo.persons?.find((person) => person.contactRole === OrgType.CUSTOMER_CONTACT) ?? null;

		return {
			companyName: this.issuedByInfo.companyName,
			contactName: '',
			countryCode: this.issuedByInfo.countryCode,
			regionCode: this.issuedByInfo.regionCode,
			cityCode: this.issuedByInfo.cityCode,
			textualPostCode: this.issuedByInfo.textualPostCode,
			locationName: this.issuedByInfo.locationName,
			phoneNumber: personInfo?.phoneNumber ?? '',
			emailAddress: personInfo?.emailAddress ?? '',
			airlineCode: this.issuedByInfo.airlineCode ?? '',
			companyType: OrgType.CARRIER,
		};
	}
}
