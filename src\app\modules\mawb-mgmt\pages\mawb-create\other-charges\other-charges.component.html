<div>
	<h3>{{ 'hawb.otherCharges.title' | translate }}</h3>

	<div class="form-container">
		<form [formGroup]="otherChargesForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'hawb.otherCharges.chargePaymentType' | translate }}</mat-label>
					<mat-select formControlName="chargePaymentType">
						<mat-option value="Prepaid">Prepaid</mat-option>
						<mat-option value="Collect">Collect</mat-option>
					</mat-select>
					@if (otherChargesForm.get('chargePaymentType')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'hawb.otherCharges.chargePaymentType' | translate }
						}}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="col-2" floatLabel="always">
					<mat-label>{{ 'hawb.otherCharges.entitlement' | translate }}</mat-label>
					<mat-select formControlName="entitlement">
						@for (entitlement of allEntitlements; track entitlement) {
							<mat-option [value]="entitlement.code">{{ entitlement.name }}</mat-option>
						}
					</mat-select>
					@if (otherChargesForm.get('entitlement')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'hawb.otherCharges.entitlement' | translate }
						}}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{ 'hawb.otherCharges.otherChargeCode' | translate }}</mat-label>
					<input type="text" matInput formControlName="otherChargeCode" [matAutocomplete]="autoOtherCharge" />
					<mat-autocomplete
						#autoOtherCharge="matAutocomplete"
						[displayWith]="displayOtherChargesName"
						(optionSelected)="onOtherChargeSelect($event)">
						@for (otherCharges of filteredOtherCharges; track otherCharges) {
							<mat-option [value]="otherCharges.code">{{ otherCharges.name }}</mat-option>
						}
					</mat-autocomplete>
					@if (otherChargesForm.get('otherChargeCode')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'hawb.otherCharges.otherChargeCode' | translate }
						}}</mat-error>
					}
				</mat-form-field>
				<orll-currency-input
					class="col-4"
					formLabel="hawb.otherCharges.otherChargeAmount"
					[fieldName]="'hawb.otherCharges.otherChargeAmount'"
					[hiddenUnit]="true"
					[currencies]="currencies"
					[currencyForm]="otherChargesForm.controls.otherChargeAmount">
				</orll-currency-input>
			</div>
			<div class="d-flex flex-row">
				<button
					mat-raised-button
					type="button"
					(keydown.enter)="addOtherCharges()"
					(click)="addOtherCharges()"
					class="align-self-end add-button">
					<mat-icon>add</mat-icon>
					{{ 'hawb.otherCharges.addButton' | translate }}
				</button>
			</div>
		</form>
	</div>

	<div class="other-charges-list">
		@for (item of otherChargesList; track $index) {
			<div class="row">
				<div class="col cell-content col-charge_payment_type">
					<span class="label-name">{{ 'hawb.otherCharges.chargePaymentType' | translate }}: </span>
					<span>{{ item.chargePaymentType }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content col-entitlement">
					<span class="label-name">{{ 'hawb.otherCharges.entitlement' | translate }}: </span>
					<span>{{ item.entitlement }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content">
					<span class="label-name">{{ 'hawb.otherCharges.otherChargeCode' | translate }}: </span>
					<span>{{ item.otherChargeCode }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content">
					<span class="label-name">{{ 'hawb.otherCharges.otherChargeAmount' | translate }}: </span>
					<span>{{ item.otherChargeAmount.numericalValue }} {{ item.otherChargeAmount.currencyUnit }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col col-delete">
					<button
						mat-icon-button
						color="primary"
						(click)="deleteOtherCharge($index)"
						[disabled]="item.disabled"
						class="orll-piece-item-panel__delete-button">
						<mat-icon>delete</mat-icon>
					</button>
				</div>
			</div>
		}
	</div>
</div>
