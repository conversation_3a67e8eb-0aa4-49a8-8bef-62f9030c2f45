import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { MawbCreateDto } from '../models/mawb-create.model';
import { HawbCreateDto } from '../../hawb-mgmt/models/hawb-create.model';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class MawbCreateRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getHawbDetail(hawbId: string) {
		return super.getData<HawbCreateDto>(`hawb-management/info`, {
			hawbId,
		});
	}

	getHawbDetailBatch(hawbId: string[]): Observable<HawbCreateDto[]> {
		return super.postData<HawbCreateDto[]>(`hawb-management/batchInfo`, hawbId);
	}

	createMawb(data: MawbCreateDto): Observable<string> {
		return super.postData<string>('mawb-management', data);
	}

	updateMawb(mawbId: string, orgId: string, data: MawbCreateDto) {
		return super.updateDataPatch<any>(`hawb-management`, { ...data, orgId, id: mawbId });
	}
}
