@use 'utils';
@use 'common-variables' as variables;

@include variables.setup-common-variables();

html,
body {
	height: 100%;
	padding: 0;
	margin: 0;
}

body {
	background-color: var(--iata-grey-50);

	.cdk-overlay-container {
		.mat-mdc-option {
			font-size: 14px;
		}
	}
}

.iata-page-content-container {
	margin: 0 utils.$block-gap;
	min-width: utils.$device-breakpoint-large - utils.$block-gap * 2;

	@include utils.respondAfter(utils.$device-breakpoint-large) {
		margin: 0 auto;
		width: utils.$device-breakpoint-large;
	}

	.mat-mdc-form-field {
		font-size: 14px;

		.mdc-text-field--outlined .mdc-floating-label {
			font-size: 14px;
		}

		.mat-mdc-text-field-wrapper {
			background-color: var(--iata-white);
		}

		.mat-mdc-form-field-icon-prefix,
		.mat-mdc-form-field-icon-suffix {
			color: var(--iata-blue-primary);
		}
	}

	.mat-mdc-checkbox {
		&.mat-mdc-checkbox-checked .mdc-checkbox__background {
			background-color: var(--iata-blue-primary) !important;
			border-color: var(--iata-blue-primary) !important;
		}

		.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
		.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
			background-color: var(--iata-blue-primary) !important;
			border-color: var(--iata-blue-primary) !important;
		}

		.mdc-checkbox__ripple {
			background-color: var(--iata-blue-primary);
		}
	}

	.mat-expansion-panel {
		border-left: 4px solid var(--iata-blue-primary);
	}

	.autocomplete-arrow {
		padding: 0 5px;
		color: var(--iata-blue-primary);
	}

	mat-radio-group mat-radio-button .mdc-label {
		font-size: 14px;
	}
}

.mat-mdc-dialog-container {
	.mat-mdc-form-field {
		.mat-mdc-text-field-wrapper {
			background-color: var(--iata-white);
		}

		.mat-mdc-form-field-icon-prefix,
		.mat-mdc-form-field-icon-suffix {
			color: var(--iata-blue-primary);
		}
	}

	.mat-mdc-checkbox {
		&.mat-mdc-checkbox-checked .mdc-checkbox__background {
			background-color: var(--iata-blue-primary) !important;
			border-color: var(--iata-blue-primary) !important;
		}

		.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
		.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
			background-color: var(--iata-blue-primary) !important;
			border-color: var(--iata-blue-primary) !important;
		}

		.mdc-checkbox__ripple {
			background-color: var(--iata-blue-primary);
		}
	}
}

.iata-box {
	padding: 20px 25px;
	margin-bottom: 40px;
	background-color: var(--iata-white);
	color: var(--iata-grey-primary);
	box-shadow:
		0 1px 4px #0000000a,
		0 8px 18px #00000014;
	border-radius: 1px;
}

.snackbar--success {
	.mdc-snackbar__surface {
		background-color: var(--iata-green-primary) !important;
	}
}

.snackbar--error {
	.mdc-snackbar__surface {
		background-color: var(--iata-red-primary) !important;
	}
}
